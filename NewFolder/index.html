<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Motor Vehicle Accident Attorney | Free Consultation | No Fees Unless We Win</title>
    <meta name="description" content="Injured in a motor vehicle accident? Get experienced legal help. Free consultation, no fees unless we win. Available 24/7 for accident victims.">

    <!-- Aeonik Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'aeonik': ['Inter', 'SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif'],
                    },
                    colors: {
                        'primary-green': '#006b54',
                        'secondary-green': '#00a475',
                        'light-green': '#e6f7f3',
                        'deep-black': '#000000',
                        'charcoal': '#1a1a1a',
                        'light-gray': '#f8f9fa',
                        'accent-orange': '#ff6b35',
                    },
                    spacing: {
                        '18': '4.5rem',
                        '22': '5.5rem',
                        '26': '6.5rem',
                        '30': '7.5rem',
                        '34': '8.5rem',
                        '38': '9.5rem',
                        '42': '10.5rem',
                        '46': '11.5rem',
                        '50': '12.5rem',
                        '128': '32rem',
                        '144': '36rem',
                        '160': '40rem',
                        '192': '48rem',
                    },
                    fontSize: {
                        '5xl': ['3rem', { lineHeight: '1.1' }],
                        '6xl': ['3.75rem', { lineHeight: '1.1' }],
                        '7xl': ['4.5rem', { lineHeight: '1.1' }],
                        '8xl': ['6rem', { lineHeight: '1.1' }],
                        '9xl': ['8rem', { lineHeight: '1.1' }],
                    },
                    maxWidth: {
                        '8xl': '90rem',
                        '9xl': '100rem',
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS for Gretta-Inspired Architectural Design -->
    <style>
        * {
            font-family: 'Inter', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
        }

        .architectural-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 0;
        }

        .hero-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
        }

        .geometric-button {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            border-radius: 50px;
            position: relative;
            overflow: hidden;
        }

        .geometric-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .geometric-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .geometric-button:hover::before {
            left: 100%;
        }

        .architectural-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .service-arrow {
            transition: all 0.3s ease;
            font-size: 24px;
            font-weight: 300;
        }

        .service-item:hover .service-arrow {
            transform: translateX(12px) scale(1.1);
        }

        .smooth-scroll {
            scroll-behavior: smooth;
        }

        /* Gretta-Style Typography */
        .hero-headline {
            font-weight: 300;
            letter-spacing: -0.03em;
            line-height: 0.85;
            font-size: clamp(3rem, 8vw, 8rem);
        }

        .hero-headline .accent {
            font-weight: 700;
            font-style: italic;
        }

        .section-headline {
            font-weight: 300;
            letter-spacing: -0.02em;
            line-height: 0.9;
            font-size: clamp(2.5rem, 6vw, 5rem);
        }

        .section-headline .accent {
            font-weight: 600;
            font-style: italic;
        }

        .body-text {
            font-weight: 300;
            line-height: 1.7;
            font-size: 18px;
        }

        .menu-text {
            font-weight: 300;
            font-size: clamp(1.5rem, 3vw, 2.5rem);
            letter-spacing: -0.01em;
        }

        /* Gretta-Style Service Menu */
        .gretta-menu {
            background: #ff6b35;
            position: relative;
        }

        .gretta-menu::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0.1) 100%);
        }

        .service-item {
            border-bottom: 1px solid rgba(255,255,255,0.15);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .service-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.08);
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .service-item:hover::before {
            left: 0;
        }

        .service-item:last-child {
            border-bottom: none;
        }

        .service-number {
            font-family: 'Courier New', monospace;
            font-weight: 400;
            opacity: 0.6;
            font-size: 14px;
        }

        /* Mobile Menu Animation */
        .mobile-menu {
            transform: translateX(100%);
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            background: rgba(255,255,255,0.95);
        }

        .mobile-menu.open {
            transform: translateX(0);
        }

        /* Card Hover Effects */
        .gretta-card {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .gretta-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0,107,84,0.05) 0%, rgba(0,164,117,0.05) 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .gretta-card:hover::before {
            opacity: 1;
        }

        .gretta-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        /* Parallax Effect */
        .parallax-bg {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }

        /* Refined Spacing */
        .gretta-spacing {
            padding: clamp(4rem, 8vw, 8rem) 0;
        }

        /* Elegant Borders */
        .elegant-border {
            border: 1px solid rgba(0,0,0,0.08);
        }

        /* Subtle Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        /* Mobile-First Mitrex-Inspired Design */
        @media (max-width: 768px) {
            .hero-headline {
                line-height: 0.9;
                font-size: clamp(2.5rem, 12vw, 4rem);
            }

            .section-headline {
                line-height: 1.0;
            }

            .menu-text {
                font-size: 1.5rem;
            }

            /* Mobile Hero - Mitrex Style */
            .mobile-hero {
                background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
                min-height: 100vh;
                position: relative;
                overflow: hidden;
            }

            .mobile-hero-content {
                position: relative;
                z-index: 10;
                padding: 120px 24px 80px;
                height: 100vh;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            .mobile-headline {
                font-size: clamp(2.5rem, 12vw, 4rem);
                font-weight: 300;
                line-height: 0.9;
                color: white;
                letter-spacing: -0.02em;
                margin-bottom: 24px;
            }

            .mobile-headline .accent {
                font-weight: 600;
                color: #ff6b35;
            }

            .mobile-tagline {
                color: rgba(255, 255, 255, 0.7);
                font-size: 18px;
                font-weight: 300;
                margin-bottom: 40px;
                line-height: 1.5;
            }

            .mobile-cta {
                background: #ff6b35;
                color: white;
                border: none;
                padding: 16px 32px;
                border-radius: 50px;
                font-size: 16px;
                font-weight: 500;
                letter-spacing: 0.5px;
                transition: all 0.3s ease;
                margin-bottom: 60px;
            }

            .mobile-cta:hover {
                background: #e55a2b;
                transform: translateY(-2px);
            }

            .mobile-scroll-indicator {
                position: absolute;
                bottom: 32px;
                left: 24px;
                color: rgba(255, 255, 255, 0.5);
                font-size: 12px;
                font-weight: 300;
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .mobile-scroll-line {
                width: 1px;
                height: 40px;
                background: rgba(255, 255, 255, 0.3);
            }

            /* Mobile Navigation - Mitrex Style */
            .mobile-nav {
                background: rgba(0, 0, 0, 0.95);
                backdrop-filter: blur(20px);
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 100;
                padding: 20px 24px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .mobile-logo {
                color: white;
                font-size: 20px;
                font-weight: 300;
                letter-spacing: 1px;
            }

            .mobile-menu-btn {
                background: none;
                border: none;
                color: white;
                padding: 8px;
            }

            .mobile-menu-btn svg {
                width: 24px;
                height: 24px;
                stroke-width: 1.5;
            }

            /* Hide desktop elements on mobile */
            .desktop-only {
                display: none !important;
            }

            /* Mobile sections */
            .mobile-section {
                padding: 80px 24px;
                background: white;
            }

            .mobile-section-dark {
                background: #1a1a1a;
                color: white;
            }

            .mobile-section h2 {
                font-size: clamp(2rem, 8vw, 3rem);
                font-weight: 300;
                line-height: 1.1;
                margin-bottom: 24px;
                color: #1a1a1a;
            }

            .mobile-section-dark h2 {
                color: white;
            }

            .mobile-section p {
                font-size: 16px;
                line-height: 1.6;
                color: rgba(26, 26, 26, 0.7);
                margin-bottom: 32px;
            }

            .mobile-section-dark p {
                color: rgba(255, 255, 255, 0.7);
            }

            /* Mobile Cards */
            .mobile-card {
                background: white;
                border-radius: 12px;
                padding: 32px 24px;
                margin-bottom: 24px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            }

            .mobile-card h3 {
                font-size: 20px;
                font-weight: 500;
                color: #1a1a1a;
                margin-bottom: 12px;
            }

            .mobile-card p {
                font-size: 14px;
                color: rgba(26, 26, 26, 0.6);
                margin: 0;
            }

            /* Mobile Contact Button */
            .mobile-contact-fixed {
                position: fixed;
                bottom: 24px;
                right: 24px;
                background: #ff6b35;
                color: white;
                border: none;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                box-shadow: 0 8px 24px rgba(255, 107, 53, 0.3);
                z-index: 50;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
            }
        }

        /* Desktop styles remain the same for larger screens */
        @media (min-width: 769px) {
            .mobile-only {
                display: none !important;
            }
        }
    </style>
</head>
<body class="smooth-scroll bg-white text-charcoal font-aeonik">

    <!-- Mobile-First Mitrex-Inspired Design -->
    <div class="mobile-only">
        <!-- Mobile Navigation -->
        <nav class="mobile-nav">
            <div class="mobile-logo">Legal</div>
            <button class="mobile-menu-btn" id="mobile-menu-toggle">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                </svg>
            </button>
        </nav>

        <!-- Mobile Hero Section -->
        <section class="mobile-hero">
            <div class="mobile-hero-content">
                <div>
                    <h1 class="mobile-headline">
                        Injured in an<br>
                        <span class="accent">accident?</span>
                    </h1>
                    <p class="mobile-tagline">
                        Get the compensation you deserve with our experienced legal team.
                    </p>
                    <button class="mobile-cta">
                        Free Consultation
                    </button>
                </div>

                <div class="mobile-scroll-indicator">
                    <div class="mobile-scroll-line"></div>
                    <span style="transform: rotate(-90deg); transform-origin: left;">SCROLL</span>
                </div>
            </div>
        </section>

        <!-- Mobile Services Section -->
        <section class="mobile-section">
            <h2>How we <span style="color: #ff6b35;">help you</span></h2>
            <p>Our experienced attorneys handle all types of motor vehicle accidents.</p>

            <div class="mobile-card">
                <h3>Car Accidents</h3>
                <p>Expert representation for vehicle collision cases</p>
            </div>

            <div class="mobile-card">
                <h3>Motorcycle Accidents</h3>
                <p>Specialized support for motorcycle injury claims</p>
            </div>

            <div class="mobile-card">
                <h3>Truck Accidents</h3>
                <p>Complex commercial vehicle accident cases</p>
            </div>
        </section>

        <!-- Mobile About Section -->
        <section class="mobile-section mobile-section-dark">
            <h2>$50M+ <span style="color: #ff6b35;">recovered</span></h2>
            <p>Over two decades of experience fighting for accident victims. No fees unless we win your case.</p>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin-top: 40px;">
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: 300; color: #ff6b35; margin-bottom: 8px;">98%</div>
                    <div style="font-size: 14px; color: rgba(255,255,255,0.7);">Success Rate</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: 300; color: #ff6b35; margin-bottom: 8px;">24/7</div>
                    <div style="font-size: 14px; color: rgba(255,255,255,0.7);">Available</div>
                </div>
            </div>
        </section>

        <!-- Mobile Contact Section -->
        <section class="mobile-section">
            <h2>Get started <span style="color: #ff6b35;">today</span></h2>
            <p>Don't wait. Contact us now for your free consultation.</p>

            <div style="margin-top: 40px;">
                <a href="tel:******-ACCIDENT" style="display: block; text-decoration: none; color: #1a1a1a; font-size: 24px; font-weight: 500; margin-bottom: 24px;">
                    (800) ACCIDENT
                </a>
                <button class="mobile-cta" style="width: 100%; margin-bottom: 0;">
                    Start Free Case Review
                </button>
            </div>
        </section>

        <!-- Fixed Contact Button -->
        <button class="mobile-contact-fixed">
            💬
        </button>
    </div>

    <!-- Desktop Design (Hidden on Mobile) -->
    <div class="desktop-only">
        <!-- Fixed Navigation Header - GRETTA STYLE -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md">
        <div class="max-w-8xl mx-auto px-8 lg:px-16">
            <nav class="flex items-center justify-between h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="text-2xl font-light text-charcoal tracking-wide">
                        Gretta
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-12">
                    <a href="#services" class="text-charcoal/70 hover:text-charcoal transition-colors font-light text-sm tracking-wide">Projects</a>
                    <a href="#about" class="text-charcoal/70 hover:text-charcoal transition-colors font-light text-sm tracking-wide">Process</a>
                    <a href="#reviews" class="text-charcoal/70 hover:text-charcoal transition-colors font-light text-sm tracking-wide">About</a>
                    <a href="#contact" class="text-charcoal/70 hover:text-charcoal transition-colors font-light text-sm tracking-wide">Contact</a>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="lg:hidden p-2">
                    <svg class="w-5 h-5 text-charcoal" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                    </svg>
                </button>
            </nav>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="mobile-menu lg:hidden fixed top-20 right-0 bottom-0 w-80">
            <div class="p-8 space-y-8">
                <a href="#services" class="block text-xl font-light text-charcoal hover:text-primary-green transition-colors">Projects</a>
                <a href="#about" class="block text-xl font-light text-charcoal hover:text-primary-green transition-colors">Process</a>
                <a href="#reviews" class="block text-xl font-light text-charcoal hover:text-primary-green transition-colors">About</a>
                <a href="#contact" class="block text-xl font-light text-charcoal hover:text-primary-green transition-colors">Contact</a>
                <div class="pt-8 border-t border-gray-200">
                    <a href="tel:******-ACCIDENT" class="block text-lg font-light text-primary-green mb-6">
                        (800) ACCIDENT
                    </a>
                    <button class="geometric-button bg-charcoal text-white px-6 py-3 font-light w-full text-sm tracking-wide">
                        Free Consultation
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section - GRETTA INSPIRED -->
    <section class="architectural-section relative min-h-screen bg-gradient-to-br from-stone-100 via-stone-50 to-white parallax-bg"
             style="background-image: url('https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80');">
        <!-- Subtle Overlay -->
        <div class="absolute inset-0 hero-overlay"></div>

        <!-- Hero Content -->
        <div class="relative z-10 max-w-8xl mx-auto px-8 lg:px-16 h-screen flex items-center">
            <div class="architectural-grid w-full gap-16">
                <!-- Left Content -->
                <div class="col-span-12 lg:col-span-6 fade-in-up">
                    <!-- Tagline -->
                    <div class="mb-8">
                        <span class="text-charcoal/60 text-lg font-light tracking-wide">Legal Excellence</span>
                    </div>

                    <!-- Main Headline -->
                    <h1 class="hero-headline text-charcoal mb-12">
                        Made <span class="accent text-primary-green">Better</span><br>
                        with <span class="accent text-charcoal">Legal Care</span>
                    </h1>

                    <!-- CTA Button -->
                    <div class="flex items-center gap-6">
                        <button class="geometric-button bg-charcoal text-white px-8 py-4 text-sm font-light tracking-wide hover:bg-primary-green flex items-center gap-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7"></path>
                            </svg>
                            View our work
                        </button>
                    </div>
                </div>

                <!-- Right Content - Featured Case -->
                <div class="col-span-12 lg:col-span-6 relative">
                    <div class="relative bg-white/80 backdrop-blur-sm p-12 elegant-border">
                        <!-- Case Number -->
                        <div class="text-charcoal/40 text-sm font-light mb-4">01</div>

                        <!-- Case Title -->
                        <h2 class="text-3xl lg:text-4xl font-light text-charcoal mb-6 leading-tight">
                            $2.4M Settlement<br>
                            <span class="text-primary-green font-normal">Motor Vehicle Case</span>
                        </h2>

                        <!-- View Project Button -->
                        <button class="geometric-button bg-transparent border border-charcoal text-charcoal px-6 py-3 text-sm font-light tracking-wide hover:bg-charcoal hover:text-white flex items-center gap-3">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7"></path>
                            </svg>
                            View Project
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-8 text-charcoal/40">
            <div class="flex items-center gap-3">
                <div class="w-px h-12 bg-charcoal/20"></div>
                <span class="text-xs font-light tracking-wider transform -rotate-90 origin-left">SCROLL</span>
            </div>
        </div>
    </section>

    <!-- Team Section - GRETTA STYLE -->
    <section class="gretta-spacing bg-white">
        <div class="max-w-8xl mx-auto px-8 lg:px-16">
            <!-- Section Header -->
            <div class="mb-20">
                <div class="text-charcoal/60 text-sm font-light tracking-wide mb-4">Our Team</div>
                <h2 class="section-headline text-charcoal mb-8">
                    Meet our <span class="accent text-primary-green">legal experts</span>
                </h2>
            </div>

            <!-- Team Grid -->
            <div class="architectural-grid gap-8">
                <!-- Team Member 1 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <div class="gretta-card bg-white elegant-border overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                             alt="Principal Attorney"
                             class="w-full h-80 object-cover">
                        <div class="p-6">
                            <div class="text-charcoal/40 text-xs font-light tracking-wide mb-2">Principal Attorney</div>
                            <h3 class="text-lg font-light text-charcoal mb-2">John Smith</h3>
                            <p class="text-sm text-charcoal/60 font-light">Specializing in motor vehicle accidents with 15+ years experience.</p>
                        </div>
                    </div>
                </div>

                <!-- Team Member 2 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <div class="gretta-card bg-white elegant-border overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                             alt="Senior Attorney"
                             class="w-full h-80 object-cover">
                        <div class="p-6">
                            <div class="text-charcoal/40 text-xs font-light tracking-wide mb-2">Senior Attorney</div>
                            <h3 class="text-lg font-light text-charcoal mb-2">Sarah Johnson</h3>
                            <p class="text-sm text-charcoal/60 font-light">Expert in complex injury cases and insurance negotiations.</p>
                        </div>
                    </div>
                </div>

                <!-- Team Member 3 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <div class="gretta-card bg-white elegant-border overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                             alt="Trial Attorney"
                             class="w-full h-80 object-cover">
                        <div class="p-6">
                            <div class="text-charcoal/40 text-xs font-light tracking-wide mb-2">Trial Attorney</div>
                            <h3 class="text-lg font-light text-charcoal mb-2">Michael Davis</h3>
                            <p class="text-sm text-charcoal/60 font-light">Experienced courtroom advocate with proven trial success.</p>
                        </div>
                    </div>
                </div>

                <!-- Contact Card -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <div class="gretta-card bg-primary-green text-white p-8 h-full flex flex-col justify-center">
                        <div class="text-white/60 text-xs font-light tracking-wide mb-4">Available 24/7</div>
                        <h3 class="text-2xl font-light mb-6">Need immediate help?</h3>
                        <div class="space-y-4">
                            <a href="tel:******-ACCIDENT" class="block text-white/90 hover:text-white transition-colors">
                                <div class="text-sm font-light">Call us now</div>
                                <div class="text-lg font-normal">(800) ACCIDENT</div>
                            </a>
                            <button class="geometric-button bg-white text-primary-green px-6 py-3 text-sm font-light tracking-wide hover:bg-stone-100 w-full">
                                Free Consultation
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section - GRETTA ORANGE MENU STYLE -->
    <section id="services" class="gretta-menu relative">
        <div class="relative z-10 max-w-8xl mx-auto px-8 lg:px-16 py-20">
            <!-- Menu Header -->
            <div class="mb-16">
                <div class="text-white/60 text-sm font-light tracking-wide mb-4">What we do</div>
            </div>

            <!-- Services Menu - Exact Gretta Style -->
            <div class="space-y-0">
                <!-- Car Accidents -->
                <div class="service-item group py-8 px-8 cursor-pointer relative z-10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-12">
                            <span class="service-number text-white/50">01</span>
                            <h3 class="menu-text text-white font-light">Car Accidents</h3>
                        </div>
                        <div class="service-arrow text-white">→</div>
                    </div>
                </div>

                <!-- Motorcycle Accidents -->
                <div class="service-item group py-8 px-8 cursor-pointer relative z-10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-12">
                            <span class="service-number text-white/50">02</span>
                            <h3 class="menu-text text-white font-light">Motorcycle Accidents</h3>
                        </div>
                        <div class="service-arrow text-white">→</div>
                    </div>
                </div>

                <!-- Truck Accidents -->
                <div class="service-item group py-8 px-8 cursor-pointer relative z-10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-12">
                            <span class="service-number text-white/50">03</span>
                            <h3 class="menu-text text-white font-light">Truck Accidents</h3>
                        </div>
                        <div class="service-arrow text-white">→</div>
                    </div>
                </div>

                <!-- Pedestrian Accidents -->
                <div class="service-item group py-8 px-8 cursor-pointer relative z-10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-12">
                            <span class="service-number text-white/50">04</span>
                            <h3 class="menu-text text-white font-light">Pedestrian Accidents</h3>
                        </div>
                        <div class="service-arrow text-white">→</div>
                    </div>
                </div>

                <!-- Bicycle Accidents -->
                <div class="service-item group py-8 px-8 cursor-pointer relative z-10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-12">
                            <span class="service-number text-white/50">05</span>
                            <h3 class="menu-text text-white font-light">Bicycle Accidents</h3>
                        </div>
                        <div class="service-arrow text-white">→</div>
                    </div>
                </div>

                <!-- Uber/Lyft Accidents -->
                <div class="service-item group py-8 px-8 cursor-pointer relative z-10">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-12">
                            <span class="service-number text-white/50">06</span>
                            <h3 class="menu-text text-white font-light">Uber/Lyft Accidents</h3>
                        </div>
                        <div class="service-arrow text-white">→</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Experience Section - GRETTA STYLE -->
    <section id="about" class="gretta-spacing bg-stone-50">
        <div class="max-w-8xl mx-auto px-8 lg:px-16">
            <div class="architectural-grid gap-16">
                <!-- Left Content -->
                <div class="col-span-12 lg:col-span-6">
                    <div class="mb-8">
                        <span class="text-charcoal/60 text-sm font-light tracking-wide">Our Expertise</span>
                    </div>

                    <h2 class="section-headline text-charcoal mb-12">
                        Two decades<br>
                        of <span class="accent text-primary-green">experience</span>
                    </h2>

                    <button class="geometric-button bg-transparent border border-charcoal text-charcoal px-6 py-3 text-sm font-light tracking-wide hover:bg-charcoal hover:text-white flex items-center gap-3">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5l7 7-7 7"></path>
                        </svg>
                        Get In Touch
                    </button>
                </div>

                <!-- Right Content - Image -->
                <div class="col-span-12 lg:col-span-6">
                    <div class="gretta-card bg-white elegant-border overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
                             alt="Legal Team"
                             class="w-full h-96 object-cover">
                        <div class="p-8">
                            <div class="text-charcoal/40 text-sm font-light mb-2">With over</div>
                            <div class="text-3xl font-light text-charcoal mb-4">$50M+ recovered</div>
                            <p class="body-text text-charcoal/70">
                                Our experienced legal team has successfully represented hundreds of motor vehicle accident victims, securing maximum compensation for their injuries and losses.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="mt-32 architectural-grid gap-8">
                <!-- Stat 1 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3 gretta-card bg-white p-8 elegant-border text-center">
                    <div class="text-3xl font-light text-primary-green mb-2">98%</div>
                    <div class="text-sm font-light text-charcoal/60 tracking-wide">Success Rate</div>
                </div>

                <!-- Stat 2 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3 gretta-card bg-white p-8 elegant-border text-center">
                    <div class="text-3xl font-light text-primary-green mb-2">$50M+</div>
                    <div class="text-sm font-light text-charcoal/60 tracking-wide">Recovered</div>
                </div>

                <!-- Stat 3 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3 gretta-card bg-white p-8 elegant-border text-center">
                    <div class="text-3xl font-light text-primary-green mb-2">24/7</div>
                    <div class="text-sm font-light text-charcoal/60 tracking-wide">Available</div>
                </div>

                <!-- Stat 4 -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3 gretta-card bg-white p-8 elegant-border text-center">
                    <div class="text-3xl font-light text-primary-green mb-2">No Fee</div>
                    <div class="text-sm font-light text-charcoal/60 tracking-wide">Unless We Win</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section class="py-32 bg-light-green">
        <div class="max-w-8xl mx-auto px-8 lg:px-16">
            <div class="text-center mb-20">
                <h2 class="section-headline text-4xl lg:text-6xl text-charcoal mb-8">
                    How We <span class="text-primary-green">Help You</span>
                </h2>
                <p class="body-text text-xl text-charcoal/80 max-w-3xl mx-auto">
                    Our proven process ensures you get the maximum compensation while we handle all the legal complexities.
                </p>
            </div>

            <div class="architectural-grid gap-8">
                <!-- Step 1 -->
                <div class="col-span-12 lg:col-span-3 text-center">
                    <div class="relative mb-8">
                        <div class="w-24 h-24 bg-primary-green rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">1</span>
                        </div>
                        <div class="hidden lg:block absolute top-12 left-full w-full h-0.5 bg-primary-green/30"></div>
                    </div>
                    <h3 class="text-2xl font-semibold text-charcoal mb-4">Free Consultation</h3>
                    <p class="body-text text-charcoal/70">We evaluate your case at no cost and explain your legal options clearly.</p>
                </div>

                <!-- Step 2 -->
                <div class="col-span-12 lg:col-span-3 text-center">
                    <div class="relative mb-8">
                        <div class="w-24 h-24 bg-secondary-green rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">2</span>
                        </div>
                        <div class="hidden lg:block absolute top-12 left-full w-full h-0.5 bg-primary-green/30"></div>
                    </div>
                    <h3 class="text-2xl font-semibold text-charcoal mb-4">Case Investigation</h3>
                    <p class="body-text text-charcoal/70">We gather evidence, interview witnesses, and build a strong case for you.</p>
                </div>

                <!-- Step 3 -->
                <div class="col-span-12 lg:col-span-3 text-center">
                    <div class="relative mb-8">
                        <div class="w-24 h-24 bg-accent-orange rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">3</span>
                        </div>
                        <div class="hidden lg:block absolute top-12 left-full w-full h-0.5 bg-primary-green/30"></div>
                    </div>
                    <h3 class="text-2xl font-semibold text-charcoal mb-4">Negotiation</h3>
                    <p class="body-text text-charcoal/70">We negotiate aggressively with insurance companies for maximum compensation.</p>
                </div>

                <!-- Step 4 -->
                <div class="col-span-12 lg:col-span-3 text-center">
                    <div class="relative mb-8">
                        <div class="w-24 h-24 bg-charcoal rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-white text-2xl font-bold">4</span>
                        </div>
                    </div>
                    <h3 class="text-2xl font-semibold text-charcoal mb-4">Trial (if necessary)</h3>
                    <p class="body-text text-charcoal/70">If needed, we take your case to trial with experienced courtroom attorneys.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="reviews" class="py-32 bg-white">
        <div class="max-w-8xl mx-auto px-8 lg:px-16">
            <div class="text-center mb-20">
                <h2 class="section-headline text-4xl lg:text-6xl text-charcoal mb-8">
                    What Our <span class="text-primary-green">Clients Say</span>
                </h2>
                <p class="body-text text-xl text-charcoal/80 max-w-3xl mx-auto">
                    Real stories from real clients who trusted us with their cases and achieved successful outcomes.
                </p>
            </div>

            <div class="architectural-grid gap-8">
                <!-- Testimonial 1 -->
                <div class="col-span-12 lg:col-span-4 bg-light-gray p-12">
                    <div class="flex items-center mb-6">
                        <div class="flex text-accent-orange">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                    <p class="body-text text-charcoal/80 mb-8 italic">
                        "After my car accident, I was overwhelmed and didn't know where to turn. This firm handled everything professionally and got me the compensation I deserved. I couldn't be more grateful."
                    </p>
                    <div>
                        <p class="font-semibold text-charcoal">Sarah M.</p>
                        <p class="text-charcoal/60">Car Accident Case</p>
                    </div>
                </div>

                <!-- Testimonial 2 -->
                <div class="col-span-12 lg:col-span-4 bg-light-gray p-12">
                    <div class="flex items-center mb-6">
                        <div class="flex text-accent-orange">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                    <p class="body-text text-charcoal/80 mb-8 italic">
                        "The attorneys were incredibly knowledgeable and fought hard for my case. They kept me informed throughout the entire process and achieved an excellent settlement."
                    </p>
                    <div>
                        <p class="font-semibold text-charcoal">Michael R.</p>
                        <p class="text-charcoal/60">Motorcycle Accident Case</p>
                    </div>
                </div>

                <!-- Testimonial 3 -->
                <div class="col-span-12 lg:col-span-4 bg-light-gray p-12">
                    <div class="flex items-center mb-6">
                        <div class="flex text-accent-orange">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                    <p class="body-text text-charcoal/80 mb-8 italic">
                        "I was hit by a truck and suffered serious injuries. This firm's dedication and expertise helped me recover compensation that covered all my medical bills and more."
                    </p>
                    <div>
                        <p class="font-semibold text-charcoal">Jennifer L.</p>
                        <p class="text-charcoal/60">Truck Accident Case</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-32 bg-light-gray">
        <div class="max-w-8xl mx-auto px-8 lg:px-16">
            <div class="text-center mb-20">
                <h2 class="section-headline text-4xl lg:text-6xl text-charcoal mb-8">
                    Frequently Asked <span class="text-primary-green">Questions</span>
                </h2>
                <p class="body-text text-xl text-charcoal/80 max-w-3xl mx-auto">
                    Get answers to common questions about motor vehicle accident cases and the legal process.
                </p>
            </div>

            <div class="max-w-4xl mx-auto space-y-4">
                <!-- FAQ Item 1 -->
                <div class="faq-item bg-white border border-gray-200">
                    <button class="faq-question w-full text-left p-8 flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <span class="text-xl font-semibold text-charcoal">What should I do immediately after a motor vehicle accident?</span>
                        <svg class="faq-icon w-6 h-6 text-primary-green transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer hidden p-8 pt-0 text-charcoal/80">
                        <p>First, ensure everyone's safety and call 911 if there are injuries. Document the scene with photos, exchange information with other drivers, and contact the police. Seek medical attention even if you feel fine, as some injuries may not be immediately apparent. Contact our firm as soon as possible to protect your rights.</p>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="faq-item bg-white border border-gray-200">
                    <button class="faq-question w-full text-left p-8 flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <span class="text-xl font-semibold text-charcoal">How much does it cost to hire an attorney?</span>
                        <svg class="faq-icon w-6 h-6 text-primary-green transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer hidden p-8 pt-0 text-charcoal/80">
                        <p>We work on a contingency fee basis, which means you don't pay attorney fees unless we win your case. Our consultation is completely free, and we'll explain all costs upfront. You can focus on recovery while we handle the legal complexities.</p>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="faq-item bg-white border border-gray-200">
                    <button class="faq-question w-full text-left p-8 flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <span class="text-xl font-semibold text-charcoal">How long do I have to file a claim?</span>
                        <svg class="faq-icon w-6 h-6 text-primary-green transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer hidden p-8 pt-0 text-charcoal/80">
                        <p>The statute of limitations varies by state, but it's typically 2-3 years from the date of the accident. However, it's crucial to contact an attorney as soon as possible to preserve evidence and protect your rights. Waiting too long can significantly impact your case.</p>
                    </div>
                </div>

                <!-- FAQ Item 4 -->
                <div class="faq-item bg-white border border-gray-200">
                    <button class="faq-question w-full text-left p-8 flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <span class="text-xl font-semibold text-charcoal">What compensation can I receive?</span>
                        <svg class="faq-icon w-6 h-6 text-primary-green transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer hidden p-8 pt-0 text-charcoal/80">
                        <p>Compensation may include medical expenses, lost wages, pain and suffering, property damage, and future medical costs. The amount depends on the severity of your injuries, the impact on your life, and the circumstances of the accident. We'll fight for maximum compensation in your case.</p>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="faq-item bg-white border border-gray-200">
                    <button class="faq-question w-full text-left p-8 flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <span class="text-xl font-semibold text-charcoal">How long does a case typically take?</span>
                        <svg class="faq-icon w-6 h-6 text-primary-green transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-answer hidden p-8 pt-0 text-charcoal/80">
                        <p>Case duration varies depending on complexity, severity of injuries, and whether a settlement can be reached. Simple cases may resolve in a few months, while complex cases involving serious injuries may take 1-2 years. We'll keep you informed throughout the process.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Urgency/Action Section -->
    <section class="py-32 bg-charcoal">
        <div class="max-w-8xl mx-auto px-8 lg:px-16 text-center">
            <h2 class="section-headline text-4xl lg:text-6xl text-white mb-8">
                Don't Wait - <span class="text-accent-orange">Get Help Now</span>
            </h2>
            <p class="body-text text-xl text-white/80 max-w-3xl mx-auto mb-12">
                Time is critical in motor vehicle accident cases. Evidence can disappear, witnesses can forget details, and legal deadlines approach quickly. Contact us today to protect your rights and maximize your compensation.
            </p>

            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
                <button class="geometric-button bg-accent-orange text-white px-12 py-6 text-xl font-medium hover:bg-orange-600">
                    Start Your Free Case Review
                </button>
                <a href="tel:******-ACCIDENT" class="geometric-button bg-transparent border-2 border-white text-white px-12 py-6 text-xl font-medium hover:bg-white hover:text-charcoal">
                    Call (800) ACCIDENT
                </a>
            </div>

            <div class="flex flex-wrap justify-center items-center gap-12 text-white/60">
                <div class="flex items-center gap-3">
                    <svg class="w-6 h-6 text-secondary-green" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Available 24/7</span>
                </div>
                <div class="flex items-center gap-3">
                    <svg class="w-6 h-6 text-secondary-green" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>Free Consultation</span>
                </div>
                <div class="flex items-center gap-3">
                    <svg class="w-6 h-6 text-secondary-green" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span>No Fees Unless We Win</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer - GRETTA STYLE -->
    <footer id="contact" class="bg-charcoal text-white py-20">
        <div class="max-w-8xl mx-auto px-8 lg:px-16">
            <div class="architectural-grid gap-16 mb-16">
                <!-- Company Info -->
                <div class="col-span-12 lg:col-span-4">
                    <div class="text-2xl font-light mb-8 tracking-wide">Gretta</div>
                    <p class="body-text text-white/70 mb-12 leading-relaxed">
                        Dedicated legal professionals providing exceptional representation for motor vehicle accident victims across the region.
                    </p>
                    <div class="text-white/50 text-sm font-light">
                        2024 Gretta Law Firm. All rights reserved.
                    </div>
                </div>

                <!-- Navigation -->
                <div class="col-span-12 md:col-span-6 lg:col-span-2">
                    <div class="space-y-6">
                        <a href="#services" class="block text-white/70 hover:text-white transition-colors font-light text-sm tracking-wide">Projects</a>
                        <a href="#about" class="block text-white/70 hover:text-white transition-colors font-light text-sm tracking-wide">Process</a>
                        <a href="#reviews" class="block text-white/70 hover:text-white transition-colors font-light text-sm tracking-wide">About</a>
                        <a href="#contact" class="block text-white/70 hover:text-white transition-colors font-light text-sm tracking-wide">Contact</a>
                    </div>
                </div>

                <!-- Social Links -->
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <div class="space-y-6">
                        <a href="#" class="block text-white/70 hover:text-white transition-colors font-light text-sm tracking-wide">Facebook</a>
                        <a href="#" class="block text-white/70 hover:text-white transition-colors font-light text-sm tracking-wide">Instagram</a>
                        <a href="#" class="block text-white/70 hover:text-white transition-colors font-light text-sm tracking-wide">Twitter</a>
                        <a href="#" class="block text-white/70 hover:text-white transition-colors font-light text-sm tracking-wide">LinkedIn</a>
                    </div>
                </div>

                <!-- Contact -->
                <div class="col-span-12 lg:col-span-3">
                    <div class="space-y-6">
                        <div>
                            <div class="text-white/50 text-xs font-light tracking-wide mb-2">Phone</div>
                            <a href="tel:******-ACCIDENT" class="text-white/70 hover:text-white transition-colors font-light">(800) ACCIDENT</a>
                        </div>
                        <div>
                            <div class="text-white/50 text-xs font-light tracking-wide mb-2">Email</div>
                            <a href="mailto:<EMAIL>" class="text-white/70 hover:text-white transition-colors font-light"><EMAIL></a>
                        </div>
                        <div>
                            <div class="text-white/50 text-xs font-light tracking-wide mb-2">Address</div>
                            <div class="text-white/70 font-light text-sm leading-relaxed">
                                123 Legal Street<br>
                                Suite 456<br>
                                City, State 12345
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="border-t border-white/10 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="flex space-x-8 mb-4 md:mb-0">
                        <a href="#" class="text-white/50 hover:text-white/70 transition-colors font-light text-xs tracking-wide">Privacy Policy</a>
                        <a href="#" class="text-white/50 hover:text-white/70 transition-colors font-light text-xs tracking-wide">Terms of Service</a>
                    </div>
                    <div class="text-white/40 font-light text-xs tracking-wide">
                        Made with care in 2024
                    </div>
                </div>
            </div>
        </div>
    </footer>
    </div> <!-- End Desktop Only -->

    <!-- JavaScript -->
    <script>
        // Mobile Menu Toggle (for both mobile and desktop)
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');

        // Handle both mobile menu buttons
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('open');
            });
        }

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                // Mobile menu functionality can be added here
                alert('Mobile menu - Contact us at (800) ACCIDENT');
            });
        }

        // Close mobile menu when clicking on links
        if (mobileMenu) {
            const mobileMenuLinks = mobileMenu.querySelectorAll('a');
            mobileMenuLinks.forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenu.classList.remove('open');
                });
            });
        }

        // FAQ Accordion
        const faqItems = document.querySelectorAll('.faq-item');

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            const answer = item.querySelector('.faq-answer');
            const icon = item.querySelector('.faq-icon');

            question.addEventListener('click', () => {
                const isOpen = !answer.classList.contains('hidden');

                // Close all other FAQ items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.querySelector('.faq-answer').classList.add('hidden');
                        otherItem.querySelector('.faq-icon').style.transform = 'rotate(0deg)';
                    }
                });

                // Toggle current item
                if (isOpen) {
                    answer.classList.add('hidden');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    answer.classList.remove('hidden');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // Smooth Scrolling for Navigation Links
        const navLinks = document.querySelectorAll('a[href^="#"]');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetSection = document.querySelector(targetId);

                if (targetSection) {
                    const offsetTop = targetSection.offsetTop - 80; // Account for fixed header
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Header Background on Scroll
        const header = document.querySelector('header');

        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                header.classList.add('bg-white/98');
                header.classList.remove('bg-white/95');
            } else {
                header.classList.add('bg-white/95');
                header.classList.remove('bg-white/98');
            }
        });

        // Click-to-Call Functionality
        const phoneLinks = document.querySelectorAll('a[href^="tel:"]');

        phoneLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Analytics tracking could be added here
                console.log('Phone call initiated');
            });
        });

        // Form Submission (placeholder)
        const ctaButtons = document.querySelectorAll('button');

        ctaButtons.forEach(button => {
            if (button.textContent.includes('Free') || button.textContent.includes('Case') || button.textContent.includes('Started')) {
                button.addEventListener('click', () => {
                    // This would typically open a contact form modal or redirect to a form page
                    alert('Contact form would open here. For now, please call (800) ACCIDENT for immediate assistance.');
                });
            }
        });

        // Service Item Interactions
        const serviceItems = document.querySelectorAll('.service-item');

        serviceItems.forEach(item => {
            item.addEventListener('click', () => {
                // This would typically navigate to a service-specific page
                const serviceName = item.querySelector('h3').textContent;
                alert(`Learn more about ${serviceName} - Contact us for a free consultation.`);
            });
        });
    </script>

</body>
</html>